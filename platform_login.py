"""
平台登录模块
对应Rust项目中的platform_login.rs
"""
import asyncio
import base64
import binascii
import hashlib
import json
import logging
import random
import string
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
from urllib.parse import urlencode, urlparse, parse_qs

import rnet
from rnet import Impersonate
from bs4 import BeautifulSoup
from pydantic import BaseModel

from proxy_manager import SimpleProxyManager, ProxyPool
from header_utils import get_auth_headers


logger = logging.getLogger(__name__)


class AuthApiError(Exception):
    """认证API错误，携带JSON数据"""
    def __init__(self, error_data: dict):
        self.error_data = error_data
        super().__init__(json.dumps(error_data))


class PlatformLoginForm(BaseModel):
    """平台登录表单"""
    username: str
    password: str


class PlatformLoginResponse(BaseModel):
    """平台登录响应"""
    access_token: str
    id_token: str
    refresh_token: str
    expires: datetime


class EmailCheckResponse(BaseModel):
    """邮箱检查响应"""
    continue_url: str
    method: str


class Auth0Config:
    """Auth0配置类"""
    
    def __init__(self, username: str, password: str, proxy_pool: ProxyPool):
        self.username = username
        self.password = password
        self.proxy_pool = proxy_pool


class Auth0:
    """Auth0认证类"""
    
    def __init__(self, config: Auth0Config):
        self.config = config
        self.proxy_manager = SimpleProxyManager(config.proxy_pool)
        
        # 会话状态
        self.oai_device_id = ""
        self.rand_num = ""
        self.platform = ""
        self.current_proxy = ""
        self.language = ""
        self.user_agent = ""
        self.session: Optional[rnet.Client] = None
        self.default_headers: Dict[str, str] = {}
        
        # 令牌状态
        self.id_token: Optional[str] = None
        self.pow_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.access_token: Optional[str] = None
        self.expires: Optional[datetime] = None
        self.org_id: Optional[str] = None
        self.proof_token: Optional[str] = None
        
        # 配置
        self.host_url = "https://auth.openai.com"
    
    async def initialize(self):
        """初始化Auth0会话"""
        logger.debug("开始初始化 Auth0")
        try:
            await self.initialize_session()
            logger.debug("Auth0 初始化成功")
        except Exception as e:
            logger.error(f"初始化 Auth0 失败: {e}")
            raise
    
    async def initialize_session(self):
        """初始化会话"""
        self.oai_device_id = str(uuid.uuid4())
        self.rand_num = self.get_rand_num()
        self.platform = self.get_platform()
        
        # 获取代理、客户端和头部
        try:
            proxy, language, ua, client, headers = await self.proxy_manager.get_proxy_and_client()
            
            logger.debug(f"使用代理: {proxy}, 语言: {language}, UA: {ua}")
            
            # 清除之前的会话状态
            self.clear_session_state()
            
            self.current_proxy = proxy
            self.language = language or "US"
            self.user_agent = ua
            self.session = client
            self.default_headers = headers
            
        except Exception as e:
            logger.error(f"获取代理失败: {e}")
            raise
    
    def clear_session_state(self):
        """清除会话状态"""
        self.id_token = None
        self.refresh_token = None
        self.access_token = None
        self.expires = None
        self.proof_token = None

    def _get_location_header(self, headers) -> Optional[str]:
        """安全获取location头部，确保返回字符串类型"""
        location = headers.get("location")
        if location is None:
            return None

        # 确保location是字符串类型
        if isinstance(location, bytes):
            location = location.decode('utf-8')

        return location
    
    async def auth(self) -> str:
        """执行认证流程"""
        logger.debug("开始 Auth0 认证")
        
        retry_exceptions = [
            "Slow Down, Check your IP.",
            "miss arkose_token",
            "Missing arkose_token",
            "Error request signup url.",
            "Failed to perform",
            "Error login.",
            "login error",
            "timed out",
            "Proof of work difficulty",
            "Failed to read response body: request or response body error: operation timed out",
            "Timeout",
            "Proxy error: status code 403 Forbidden",
            "Login failed.",
            "Failed to solve proof of work",
            "Login error: timed out",
            "Failed to check email",
            "Failed to get sentinel token",
            "Proxy error",
            "error sending request",
            "Unknown Redirect: https://platform.open",
            "Failed to",
            "Unknown login error, status code: 429",
            "429 Too Many Requests",
            "POW token",
            "connection reset",
            "connection refused",
            "no route to host",
            "network unreachable",
            "Proxy error: status code 429 Too Many Requests",
            "Login error: error sending request for url",
            "error sending request for url",
            "error sending request",
            "builder error",
            "operation timed out",
            "unexpected eof",
            "dns error",
        ]
        
        last_error = None
        start_time = time.time()
        used_proxies = set()
        
        for attempt in range(12):
            # 添加当前代理到已使用列表
            used_proxies.add(self.current_proxy)
            
            if time.time() - start_time > 45:
                logger.warning("Auth 方法执行时间超过45秒，返回最后一次错误")
                raise Exception(last_error or "timeout")
            
            # 检查现有令牌是否有效
            if self.access_token and self.expires:
                if self.expires > datetime.now():
                    return self.access_token
            
            try:
                result = await self.generate_random_code()
                logger.info("认证成功")
                return result
                
            except Exception as e:
                error_message = str(e)
                logger.info(f"{self.config.username} ｜ 认证失败: {error_message}")
                
                # 检查是否需要重试
                need_retry = any(exception in error_message for exception in retry_exceptions)
                
                if need_retry and attempt < 11:
                    # 对于超时错误，立即切换代理
                    if "timed out" in error_message or "timeout" in error_message:
                        current = self.current_proxy
                        await self.proxy_manager.remove_proxy(current)
                        logger.warning("检测到超时错误，立即切换新代理")
                        
                        try:
                            await self.initialize_session()
                            if self.current_proxy in used_proxies:
                                logger.warning("获取到之前使用过的代理，重新请求新代理")
                                continue
                        except Exception as init_err:
                            logger.error(f"重新初始化会话失败: {init_err}")
                            raise init_err
                    else:
                        # 其他错误使用退避策略
                        await self.proxy_manager.remove_proxy(self.current_proxy)
                        wait_time = min(2 ** attempt, 5)
                        await asyncio.sleep(wait_time)
                        
                        try:
                            await self.initialize_session()
                            if self.current_proxy in used_proxies:
                                logger.warning("获取到之前使用过的代理，重新请求新代理")
                                continue
                        except Exception as init_err:
                            logger.error(f"重新初始化会话失败: {init_err}")
                            raise init_err
                    
                    last_error = error_message
                else:
                    raise e
        
        raise Exception(last_error or "多次尝试后认证失败")

    async def generate_random_code(self) -> str:
        """生成随机代码并开始认证流程"""
        code_verifier = self.generate_code_verifier()
        code_challenge = self.generate_code_challenge(code_verifier)
        nonce = self.generate_nonce()
        state = self.generate_state()

        # 构建授权URL
        params = {
            "issuer":"https://auth.openai.com",
            "client_id": "app_2SKx67EdpoN0G6j64rFvigXD",
            "audience": "https://api.openai.com/v1",
            "redirect_uri": "https://platform.openai.com/auth/callback",
            "max_age": "0",
            "scope": "openid profile email offline_access",
            "response_type": "code",
            "response_mode": "query",
            "state": state,
            "nonce": nonce,
            "code_challenge": code_challenge,
            "code_challenge_method": "S256",
            "auth0Client": "eyJuYW1lIjoiYXV0aDAtc3BhLWpzIiwidmVyc2lvbiI6IjEuMjEuMCJ9",
            "flow": "control",
            "device_id": self.oai_device_id,
        }

        url = f"{self.host_url}/api/accounts/authorize?{urlencode(params)}"
        return await self.ios_login(code_verifier, url)

    async def ios_login(self, code_verifier: str, url: str) -> str:
        """iOS登录流程"""
        max_redirects = 10
        redirect_count = 0
        current_url = url
        headers = self.default_headers.copy()

        while redirect_count < max_redirects:
            try:
                resp = await self.session.get(
                    current_url,
                    proxy=self.current_proxy,
                    headers=headers
                )

                logger.info(f"响应状态码: {resp.status}")

                if resp.status == 302:
                    location = self._get_location_header(resp.headers)
                    if not location:
                        raise Exception("Missing location header")

                    current_url = location
                    redirect_count += 1
                    continue

                elif resp.status == 403:
                    raise Exception(f"Proxy error: status code {resp.status}")

                elif resp.status == 429:
                    raise Exception(f"Proxy error: status code {resp.status}")

                elif resp.status == 200:
                    if current_url.startswith("https://auth.openai.com/log-in"):
                        email_check = await self.check_email()
                        logger.info(f"邮箱验证结果: {email_check}")
                        return await self.auth0_login(code_verifier, email_check.continue_url)
                    else:
                        raise Exception(f"Unknown Redirect: {current_url}")
                else:
                    logger.error(f"请求失败，状态码: {resp.status}")
                    raise Exception("Error requesting signup URL.")

            except rnet.TimeoutError:
                raise Exception("Request timeout")
            except Exception as e:
#                 logger.error(f"请求失败: {e}")
                raise Exception(f"{e}")

        raise Exception("Maximum redirect attempts reached")

    async def get_pow_local(self) -> Dict[str, str]:
        if self.pow_token:
            return self.pow_token

        """获取本地POW令牌"""
        from proofof_work import get_config, get_requirements_token, get_answer_token

        for _ in range(3):  # 最多尝试3次
            try:
                # 获取配置
                config = get_config(self.user_agent)
                
                # 获取 requirements token
                p = get_requirements_token(config)
                
                # 准备请求数据
                data = {
                    "p": p,
                    "id": self.oai_device_id,
                    "flow": "password_verify__auto"
                }
                
                # 准备请求头
                headers = self.default_headers.copy()
                headers.update({
                    "content-type": "text/plain;charset=UTF-8",
                    "accept": "*/*",
                    "sec-fetch-site": "cross-site",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-dest": "empty"
                })
                
                # 发送请求
                start_time = time.time()
                resp = await self.session.post(
                    "https://chatgpt.com/backend-api/sentinel/req",
                    proxy=self.current_proxy,
                    headers=headers,
                    json=data,
                    timeout=5
                )
                elapsed = time.time() - start_time
                logger.info(f"POW token 请求耗时: {elapsed:.2f}s")
                logger.info(f"POW token 请求状态码: {resp.status}")
                
                # 解析响应
                token_data = await resp.json()
                
                # 检查是否需要 proof of work
                proofofwork = token_data.get("proofofwork", {})
                proofofwork_required = proofofwork.get("required", False)
                
                if proofofwork_required:
                    proofofwork_diff = proofofwork.get("difficulty", "")
                    proofofwork_seed = proofofwork.get("seed", "")
                    
                    # 使用本地计算
                    answer_token, solved = get_answer_token(proofofwork_seed, proofofwork_diff, config)
                    logger.info(f"本地计算是否成功: {solved}")
                    
                    if not solved:
                        raise Exception("Failed to solve proof of work")
                    
                    # 设置 proof_token
                    self.proof_token = f"{answer_token}"
                
                # 构建最终的 POW token
                pow_token = {
                    "p": self.proof_token or "",
                    "t": base64.b64encode(self.generate_random_key().encode()).decode(),
                    "c": token_data.get("token", ""),
                    "id": self.oai_device_id,
                    "flow": "password_verify"
                }
                
                return pow_token
                
            except Exception as e:
                logger.error(f"POW token 请求失败: {e}")
                if _ == 2:  # 最后一次尝试
                    raise Exception("Failed to get POW token after 3 attempts")
                continue

    async def auth0_login(self, code_verifier: str, state: str) -> str:
        """Auth0登录"""
        headers = self.default_headers.copy()
        headers["referer"] = state

        state_url = "https://auth.openai.com/api/accounts/password/verify"
        logger.debug(f"{self.config.username} 平台登录打码中，请稍等...")

        sentinel_token = await self.get_pow_local()
        data = {"password": self.config.password}
        sentinel_token_json = json.dumps(sentinel_token)
        headers["openai-sentinel-token"] = sentinel_token_json
        headers["content-type"] = "application/json"

        try:
            resp = await self.session.post(
                state_url,
                proxy=self.current_proxy,
                headers=headers,
                json=data
            )

            logger.info(f"{self.config.username} ｜ 登录响应状态码: {resp.status}")

            if resp.status == 200:
                resp_json = await resp.json()
                continue_url = resp_json.get("continue_url")

                if not continue_url:
                    raise Exception("Missing continue_url in response JSON")

                if "oauth2/auth?audience" in continue_url:
                    return await self.callback_jump(code_verifier, continue_url)
                else:
                    raise Exception(f"Unknown Redirect Login: {continue_url}")

            elif resp.status == 302:
                location = self._get_location_header(resp.headers) or ""

                logger.info(f"错误地址响应状态码: {location}")
                logger.info(f"当前代理地址: {self.current_proxy}")

                # 检查payload参数
                parsed_url = urlparse(location)
                query_params = parse_qs(parsed_url.query)

                if "payload" in query_params:
                    payload = query_params["payload"][0]
                    try:
                        # 解码payload
                        decoded = base64.b64decode(payload)
                        error_json = json.loads(decoded)
                        logger.info(f"获取到错误响应: {error_json}")
                        # 抛出携带JSON数据的自定义异常
                        raise AuthApiError(error_json)
                    except (binascii.Error, json.JSONDecodeError) as decode_error:
                        logger.warning(f"无法解析错误payload: {decode_error}")
                        # 如果无法解析payload，继续执行后续逻辑
                        pass

                raise Exception(f"Unknown Redirect: {location}")

            elif resp.status in [301, 303]:
                raise Exception("Missing arkose_token")

            elif resp.status == 400:
                body = await resp.text()
                soup = BeautifulSoup(body, 'html.parser')

                error_elements = soup.select("[id^='error-element-password']")
                prompt_elements = soup.select("[id^='prompt-alert']")

                error_text = ""

                for element in error_elements:
                    text = element.get_text().strip()
                    if text == "Incorrect email address, phone number, or password. Phone numbers must include the country code.":
                        error_text += "Wrong email or password."
                    else:
                        error_text += text

                for element in prompt_elements:
                    error_text += element.get_text().strip()

                if error_text:
                    raise Exception(error_text)
                else:
                    logger.error("未知的登录错误，无法提取错误信息")
                    raise Exception("Unknown login error, status code: 400")
            else:
                raise Exception(f"Unknown login error, status code: {resp.status}")

        except rnet.TimeoutError:
            raise Exception("Login request timeout")
        except AuthApiError:
            # 直接重新抛出AuthApiError，不进行包装
            raise
        except Exception as e:
            raise Exception(str(e))

    async def callback_jump(self, code_verifier: str, location: str) -> str:
        """回调跳转"""
        url = location
        logger.debug(f"发送回调请求到: {url}")

        headers = self.default_headers.copy()

        try:
            resp = await self.session.get(url, proxy=self.current_proxy, headers=headers)
            logger.debug(f"回调响应状态码: {resp.status}")

            if resp.status in [302, 303]:
                location = self._get_location_header(resp.headers)
                if not location:
                    raise Exception("Missing location header")

                logger.debug(f"重定向到: {location}")

                if "mfa-otp-challenge" in location:
                    raise Exception("MFA required.")
                elif "https://auth.openai.com/login_challenge" in location:
                    raise Exception("Two-step email verification")
                elif location.startswith("https://platform.openai.com/auth/callback?"):
                    logger.debug("检测到平台回调URL，准备获取访问令牌")
                    return await self.get_access_token(code_verifier, location)
                elif location.startswith("https://auth.openai.com/api/accounts/callback/auth0?"):
                    logger.debug("检测到Auth0回调URL，准备获取访问令牌")
                    return await self.get_access_token(code_verifier, location)
                elif location.startswith("https://auth.openai.com/api/accounts/consent?"):
                    return await self.callback_jump(code_verifier, location)
                elif location.startswith("https://auth.openai.com/api/oauth/oauth2/auth?"):
                    return await self.callback_jump(code_verifier, location)
                else:
                    logger.info(f"未知的回调地址: {location}")
                    raise Exception("Login callback failed.")
            else:
                logger.error(f"回调跳转失败，状态码: {resp.status}")
                raise Exception("Callback jump failed.")

        except rnet.TimeoutError:
            raise Exception("Callback jump timeout")
        except Exception as e:
            logger.error(f"回调请求失败: {e}")
            raise Exception(f"Callback jump timeout: {e}")

    async def get_access_token(self, code_verifier: str, callback_url: str) -> str:
        """获取访问令牌"""
        parsed_url = urlparse(callback_url)
        url_params = parse_qs(parsed_url.query)

        if "error" in url_params:
            error = url_params["error"][0]
            error_description = url_params.get("error_description", [""])[0]
            raise Exception(f"{error}: {error_description}")

        if "code" not in url_params:
            raise Exception("Error getting code from callback URL.")

        code = url_params["code"][0]

        url = f"{self.host_url}/oauth/token"
        headers = self.default_headers.copy()
        headers["content-type"] = "application/json"

        data = {
            "redirect_uri": "https://platform.openai.com/auth/callback",
            "grant_type": "authorization_code",
            "client_id": "app_2SKx67EdpoN0G6j64rFvigXD",
            "code": code,
            "code_verifier": code_verifier,
        }

        try:
            resp = await self.session.post(url, proxy=self.current_proxy, headers=headers, json=data)

            if resp.status == 200:
                json_data = await resp.json()

                access_token = json_data.get("access_token")
                refresh_token = json_data.get("refresh_token")
                id_token = json_data.get("id_token")
                expires_in = json_data.get("expires_in")

                if not all([access_token, refresh_token, id_token, expires_in]):
                    raise Exception("Failed to get required tokens.")

                self.id_token = id_token
                self.refresh_token = refresh_token
                self.access_token = access_token
                self.expires = datetime.now() + timedelta(seconds=expires_in)

                return access_token
            else:
                error_text = await resp.text()
                raise Exception(f"Error getting access token: {error_text}")

        except rnet.TimeoutError:
            raise Exception("Get access token timeout")
        except Exception as e:
            raise Exception(f"获取访问令牌请求时: {e}")

    async def check_email(self) -> EmailCheckResponse:
        """检查邮箱"""
        url = f"{self.host_url}/api/accounts/authorize/continue"
        headers = self.default_headers.copy()
        headers["content-type"] = "application/json"

        pow_token = await self.get_pow_local()
        pow_token_str = json.dumps(pow_token)
        headers["openai-sentinel-token"] = pow_token_str

        data = {
            "username": {
                "kind": "email",
                "value": self.config.username
            }
        }

        try:
            resp = await self.session.post(url, proxy=self.current_proxy, headers=headers, json=data)

            if resp.status == 200:
                json_data = await resp.json()

                continue_url = json_data.get("continue_url")
                method = json_data.get("method")

                if not continue_url or not method:
                    raise Exception("Missing continue_url or method in response")

                return EmailCheckResponse(
                    continue_url=continue_url,
                    method=method
                )
            else:
                logger.info(f"resp.status{resp.status}")
                raise Exception("Failed to check email.")

        except rnet.TimeoutError:
            raise Exception("Check email timeout")
        except Exception as e:
            raise Exception(f"Request timed out, failed to check email: {e}")

    # 工具方法
    def get_rand_num(self) -> str:
        """获取随机数"""
        return str(random.randint(0, 1))

    def get_platform(self) -> str:
        """获取平台"""
        return random.choice(["Windows", "MacIntel"])

    def generate_code_verifier(self) -> str:
        """生成代码验证器"""
        token = ''.join(random.choices(string.ascii_letters + string.digits + '-._~', k=32))
        return base64.urlsafe_b64encode(token.encode()).decode().rstrip('=')

    def generate_code_challenge(self, code_verifier: str) -> str:
        """生成代码挑战"""
        digest = hashlib.sha256(code_verifier.encode()).digest()
        return base64.urlsafe_b64encode(digest).decode().rstrip('=')

    def generate_nonce(self) -> str:
        """生成随机数"""
        nonce = ''.join(random.choices(string.ascii_letters + string.digits, k=54))
        return f"{nonce}3D%3D"

    def generate_state(self) -> str:
        """生成状态"""
        state = ''.join(random.choices(string.ascii_letters + string.digits, k=43))
        return base64.b64encode(state.encode()).decode()

    def generate_random_key(self) -> str:
        """生成随机密钥"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=32))


# 平台登录API函数
async def platform_login(form: PlatformLoginForm, proxy_pool: ProxyPool) -> PlatformLoginResponse:
    """平台登录API"""
    config = Auth0Config(
        username=form.username,
        password=form.password,
        proxy_pool=proxy_pool
    )

    logger.info(f"{form.username} | {form.password} ｜ 平台登录请求接收")

    # 设置超时
    timeout_duration = 50
    start_time = time.time()

    try:
        # 创建Auth0实例
        auth = Auth0(config)

        # 初始化
        await auth.initialize()
        elapsed = time.time() - start_time
        logger.info(f"{form.username} ｜ 代理准备完毕，耗时: {elapsed:.2f}s")

        # 执行认证
        access_token = await auth.auth()

        response = PlatformLoginResponse(
            access_token=access_token,
            id_token=auth.id_token,
            refresh_token=auth.refresh_token,
            expires=auth.expires or datetime.now()
        )

        return response

    except asyncio.TimeoutError:
        logger.error(f"登录请求超时(超过{timeout_duration}秒)")
        raise Exception("timeout")
    except AuthApiError as e:
        logger.error(f"认证API失败: {e.error_data}")
        # 直接抛出包含JSON数据的异常
        raise e
    except Exception as e:
#         logger.error(f"登录失败: {e}")
        raise e
    finally:
        # 清理资源
        if 'auth' in locals() and auth.session:
            # rnet.Client 不需要显式关闭
            pass
